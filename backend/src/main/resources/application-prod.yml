# application-prod.yml
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    show-sql: false
  
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=60m
    cache-names:
      - users
      - novels
      - blogs
      - stories

server:
  port: 8080
  ssl:
    enabled: false # SSL handled by nginx

logging:
  level:
    root: WARN
    com.jsmith.author: INFO
    org.springframework: WARN
  file:
    name: /var/log/app/application.log

management:
  endpoint:
    health:
      show-details: never
