# application-prod.yml
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    show-sql: false
  
  cache:
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: false
  
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

server:
  port: 8080
  ssl:
    enabled: false # SSL handled by nginx

logging:
  level:
    root: WARN
    com.jsmith.author: INFO
    org.springframework: WARN
  file:
    name: /var/log/app/application.log

management:
  endpoint:
    health:
      show-details: never