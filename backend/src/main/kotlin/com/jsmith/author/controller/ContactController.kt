// controller/ContactController.kt
package com.jsmith.author.controller

import com.jsmith.author.dto.ContactRequest
import com.jsmith.author.entity.ContactMessage
import com.jsmith.author.repository.ContactRepository
import com.jsmith.author.service.EmailService
import jakarta.servlet.http.HttpServletRequest
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/contact")
class ContactController(
    private val contactRepository: ContactRepository,
    private val emailService: EmailService
) {
    
    @PostMapping
    fun submitContact(
        @Valid @RequestBody request: ContactRequest,
        servletRequest: HttpServletRequest
    ): ResponseEntity<Map<String, String>> {
        val message = ContactMessage(
            name = request.name,
            email = request.email,
            subject = request.subject,
            message = request.message,
            ipAddress = servletRequest.remoteAddr
        )
        
        contactRepository.save(message)
        
        // Notify admin
        // emailService.notifyContactMessage(message)
        
        return ResponseEntity.ok(mapOf("message" to "Thank you for your message. We'll get back to you soon!"))
    }
}